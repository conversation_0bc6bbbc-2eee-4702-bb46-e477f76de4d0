FROM gradle:8.5-jdk21 AS build
WORKDIR /app


# Copy only the build files first
COPY build.gradle settings.gradle ./
COPY gradle ./gradle
# Download dependencies (this layer will be cached)
RUN gradle dependencies --no-daemon

# Copy source code and build
COPY src ./src
RUN gradle build --no-daemon

FROM openjdk:21-slim
WORKDIR /app
COPY --from=build /app/build/libs/*.jar app.jar

EXPOSE 8095
ENTRYPOINT ["java", "-jar", "app.jar"] 