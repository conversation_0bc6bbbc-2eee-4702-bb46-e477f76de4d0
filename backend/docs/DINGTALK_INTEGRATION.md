# 钉钉集成功能说明

## 概述

本系统集成了钉钉API，支持以下功能：
- 创建、更新、删除日历事件
- 查询可用会议室
- 预订会议室

## 配置说明

### 1. 基础配置

在 `application.properties` 中配置：

```properties
# 启用钉钉集成
dingtalk.enabled=true

# 钉钉应用配置（需要在钉钉开放平台创建应用获取）
dingtalk.app.key=your_app_key
dingtalk.app.secret=your_app_secret

# API配置
dingtalk.api.base-url=https://oapi.dingtalk.com
dingtalk.api.timeout=30000
dingtalk.api.retry-count=3

# 日历功能配置
dingtalk.calendar.enabled=true
dingtalk.calendar.default-calendar-id=your_calendar_id

# 会议室功能配置
dingtalk.meeting-room.enabled=true
dingtalk.meeting-room.default-rooms=会议室1,会议室2,会议室3,面试室A,面试室B
```

### 2. 钉钉应用配置

1. 登录钉钉开放平台：https://open.dingtalk.com/
2. 创建企业内部应用
3. 获取 AppKey 和 AppSecret
4. 配置应用权限：
   - 日历管理权限
   - 会议室管理权限
   - 通讯录读取权限

## API使用说明

### 1. 日历事件管理

```java
@Autowired
private DingTalkService dingTalkService;

// 创建日历事件
String eventId = dingTalkService.createCalendarEvent(interview, organizer);

// 更新日历事件
boolean success = dingTalkService.updateCalendarEvent(eventId, interview, organizer);

// 删除日历事件
boolean success = dingTalkService.deleteCalendarEvent(eventId);
```

### 2. 会议室管理

```java
// 获取可用会议室
List<String> rooms = dingTalkService.getAvailableMeetingRooms(startTime, endTime);

// 预订会议室
boolean success = dingTalkService.bookMeetingRoom(roomId, interview, organizer);
```

### 3. 配置验证

```java
// 验证配置是否正确
boolean isValid = dingTalkService.validateConfiguration();
```

## 测试接口

系统提供了测试接口用于验证钉钉集成功能：

### 1. 测试配置
```
GET /api/dingtalk/test/config
```

### 2. 测试访问令牌
```
GET /api/dingtalk/test/token
```

### 3. 测试会议室查询
```
GET /api/dingtalk/test/meeting-rooms?startTime=2024-01-01 09:00:00&endTime=2024-01-01 10:00:00
```

### 4. 测试服务配置
```
GET /api/dingtalk/test/service-config
```

### 5. 测试日历事件
```
# 创建日历事件
POST /api/dingtalk/test/calendar-event

# 更新日历事件
PUT /api/dingtalk/test/calendar-event/{eventId}

# 删除日历事件
DELETE /api/dingtalk/test/calendar-event/{eventId}

# 测试完整集成流程
POST /api/dingtalk/test/integration/full-flow
```

## 集成服务使用

系统提供了 `InterviewDingTalkIntegrationService` 服务，封装了面试与钉钉的完整集成逻辑：

```java
@Autowired
private InterviewDingTalkIntegrationService integrationService;

// 面试创建时的集成处理
String eventId = integrationService.handleInterviewCreated(interview, organizer);

// 面试更新时的集成处理
boolean success = integrationService.handleInterviewUpdated(interview, eventId, organizer);

// 面试删除时的集成处理
boolean success = integrationService.handleInterviewDeleted(eventId);

// 获取可用会议室
List<String> rooms = integrationService.getAvailableMeetingRooms(startTime, endTime);

// 检查集成是否可用
boolean available = integrationService.isDingTalkIntegrationAvailable();
```

## 错误处理

系统会自动处理以下错误情况：
1. 访问令牌过期自动刷新
2. API调用失败时返回默认值
3. 网络异常时的重试机制
4. 配置无效时的降级处理

## 注意事项

1. **权限配置**：确保钉钉应用具有相应的API调用权限
2. **网络访问**：确保服务器能够访问钉钉API服务器
3. **令牌管理**：系统会自动管理访问令牌的获取和刷新
4. **错误日志**：所有API调用错误都会记录在日志中
5. **降级策略**：当钉钉API不可用时，系统会使用默认配置继续工作

## 开发调试

1. 启用调试日志：
```properties
logging.level.com.redteamobile.talentsphere.service.impl.DingTalkServiceImpl=DEBUG
logging.level.com.redteamobile.talentsphere.util.DingTalkApiClient=DEBUG
```

2. 使用测试接口验证功能
3. 查看应用日志了解API调用详情

## 生产环境部署

1. 确保配置了正确的钉钉应用凭据
2. 验证网络连接和防火墙设置
3. 监控API调用频率和错误率
4. 定期检查访问令牌的有效性
