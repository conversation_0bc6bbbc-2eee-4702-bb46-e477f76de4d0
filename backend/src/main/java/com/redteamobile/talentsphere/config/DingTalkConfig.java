package com.redteamobile.talentsphere.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 钉钉配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "dingtalk")
public class DingTalkConfig {
    
    /**
     * 是否启用钉钉集成
     */
    private boolean enabled = false;
    
    /**
     * 钉钉应用配置
     */
    private App app = new App();
    
    /**
     * 钉钉API配置
     */
    private Api api = new Api();
    
    /**
     * 日历配置
     */
    private Calendar calendar = new Calendar();
    
    /**
     * 会议室配置
     */
    private MeetingRoom meetingRoom = new MeetingRoom();
    
    @Data
    public static class App {
        private String key;
        private String secret;
    }
    
    @Data
    public static class Api {
        private String baseUrl = "https://oapi.dingtalk.com";
        private int timeout = 30000; // 30秒超时
        private int retryCount = 3;
    }
    
    @Data
    public static class Calendar {
        private boolean enabled = false;
        private String defaultCalendarId;
    }
    
    @Data
    public static class MeetingRoom {
        private boolean enabled = false;
        private String[] defaultRooms = {"会议室1", "会议室2", "会议室3"};
    }
} 