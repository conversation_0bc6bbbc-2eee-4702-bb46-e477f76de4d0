package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.service.DingTalkService;
import com.redteamobile.talentsphere.util.DingTalkApiClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 钉钉集成测试控制器
 * 用于测试钉钉API集成功能
 */
@RestController
@RequestMapping("/api/dingtalk/test")
public class DingTalkTestController {
    
    @Autowired
    private DingTalkService dingTalkService;
    
    @Autowired
    private DingTalkApiClient dingTalkApiClient;
    
    /**
     * 测试钉钉配置
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> testConfig() {
        Map<String, Object> result = new HashMap<>();
        
        boolean isConfigValid = dingTalkApiClient.validateConfiguration();
        result.put("configValid", isConfigValid);
        
        if (isConfigValid) {
            result.put("message", "钉钉配置有效");
        } else {
            result.put("message", "钉钉配置无效或未启用");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 测试获取访问令牌
     */
    @GetMapping("/token")
    public ResponseEntity<Map<String, Object>> testGetToken() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String token = dingTalkApiClient.getAccessToken();
            if (token != null) {
                result.put("success", true);
                result.put("message", "成功获取访问令牌");
                result.put("tokenPrefix", token.substring(0, Math.min(10, token.length())) + "...");
            } else {
                result.put("success", false);
                result.put("message", "获取访问令牌失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取访问令牌异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 测试获取可用会议室
     */
    @GetMapping("/meeting-rooms")
    public ResponseEntity<Map<String, Object>> testGetMeetingRooms(
            @RequestParam(defaultValue = "2024-01-01 09:00:00") String startTime,
            @RequestParam(defaultValue = "2024-01-01 10:00:00") String endTime) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<String> rooms = dingTalkService.getAvailableMeetingRooms(startTime, endTime);
            result.put("success", true);
            result.put("message", "成功获取会议室列表");
            result.put("rooms", rooms);
            result.put("count", rooms.size());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取会议室列表异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 测试钉钉服务配置验证
     */
    @GetMapping("/service-config")
    public ResponseEntity<Map<String, Object>> testServiceConfig() {
        Map<String, Object> result = new HashMap<>();
        
        boolean isValid = dingTalkService.validateConfiguration();
        result.put("configValid", isValid);
        result.put("message", isValid ? "钉钉服务配置有效" : "钉钉服务配置无效");
        
        return ResponseEntity.ok(result);
    }
}
