package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.service.DingTalkService;
import com.redteamobile.talentsphere.service.impl.InterviewDingTalkIntegrationService;
import com.redteamobile.talentsphere.util.DingTalkApiClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 钉钉集成测试控制器
 * 用于测试钉钉API集成功能
 */
@RestController
@RequestMapping("/api/dingtalk/test")
public class DingTalkTestController {
    
    @Autowired
    private DingTalkService dingTalkService;

    @Autowired
    private DingTalkApiClient dingTalkApiClient;

    @Autowired
    private InterviewDingTalkIntegrationService integrationService;
    
    /**
     * 测试钉钉配置
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> testConfig() {
        Map<String, Object> result = new HashMap<>();
        
        boolean isConfigValid = dingTalkApiClient.validateConfiguration();
        result.put("configValid", isConfigValid);
        
        if (isConfigValid) {
            result.put("message", "钉钉配置有效");
        } else {
            result.put("message", "钉钉配置无效或未启用");
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 测试获取访问令牌
     */
    @GetMapping("/token")
    public ResponseEntity<Map<String, Object>> testGetToken() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String token = dingTalkApiClient.getAccessToken();
            if (token != null) {
                result.put("success", true);
                result.put("message", "成功获取访问令牌");
                result.put("tokenPrefix", token.substring(0, Math.min(10, token.length())) + "...");
            } else {
                result.put("success", false);
                result.put("message", "获取访问令牌失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取访问令牌异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 测试获取可用会议室
     */
    @GetMapping("/meeting-rooms")
    public ResponseEntity<Map<String, Object>> testGetMeetingRooms(
            @RequestParam(defaultValue = "2024-01-01 09:00:00") String startTime,
            @RequestParam(defaultValue = "2024-01-01 10:00:00") String endTime) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<String> rooms = dingTalkService.getAvailableMeetingRooms(startTime, endTime);
            result.put("success", true);
            result.put("message", "成功获取会议室列表");
            result.put("rooms", rooms);
            result.put("count", rooms.size());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取会议室列表异常: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 测试钉钉服务配置验证
     */
    @GetMapping("/service-config")
    public ResponseEntity<Map<String, Object>> testServiceConfig() {
        Map<String, Object> result = new HashMap<>();

        boolean isValid = dingTalkService.validateConfiguration();
        result.put("configValid", isValid);
        result.put("message", isValid ? "钉钉服务配置有效" : "钉钉服务配置无效");

        return ResponseEntity.ok(result);
    }

    /**
     * 测试创建日历事件
     */
    @PostMapping("/calendar-event")
    public ResponseEntity<Map<String, Object>> testCreateCalendarEvent() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建测试面试对象
            Interview testInterview = createTestInterview();

            String eventId = dingTalkService.createCalendarEvent(testInterview, "<EMAIL>");

            if (eventId != null) {
                result.put("success", true);
                result.put("message", "成功创建日历事件");
                result.put("eventId", eventId);
            } else {
                result.put("success", false);
                result.put("message", "创建日历事件失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建日历事件异常: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 测试更新日历事件
     */
    @PutMapping("/calendar-event/{eventId}")
    public ResponseEntity<Map<String, Object>> testUpdateCalendarEvent(@PathVariable String eventId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建测试面试对象
            Interview testInterview = createTestInterview();
            testInterview.setCandidate("更新后的候选人");

            boolean success = dingTalkService.updateCalendarEvent(eventId, testInterview, "<EMAIL>");

            result.put("success", success);
            result.put("message", success ? "成功更新日历事件" : "更新日历事件失败");
            result.put("eventId", eventId);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新日历事件异常: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 测试删除日历事件
     */
    @DeleteMapping("/calendar-event/{eventId}")
    public ResponseEntity<Map<String, Object>> testDeleteCalendarEvent(@PathVariable String eventId) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = dingTalkService.deleteCalendarEvent(eventId);

            result.put("success", success);
            result.put("message", success ? "成功删除日历事件" : "删除日历事件失败");
            result.put("eventId", eventId);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除日历事件异常: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 测试完整的面试钉钉集成流程
     */
    @PostMapping("/integration/full-flow")
    public ResponseEntity<Map<String, Object>> testFullIntegrationFlow() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建测试面试
            Interview testInterview = createTestInterview();
            String organizer = "<EMAIL>";

            // 1. 测试面试创建集成
            String eventId = integrationService.handleInterviewCreated(testInterview, organizer);
            result.put("createSuccess", eventId != null);
            result.put("eventId", eventId);

            if (eventId != null) {
                // 2. 测试面试更新集成
                testInterview.setCandidate("更新后的候选人");
                boolean updateSuccess = integrationService.handleInterviewUpdated(testInterview, eventId, organizer);
                result.put("updateSuccess", updateSuccess);

                // 3. 测试面试删除集成
                boolean deleteSuccess = integrationService.handleInterviewDeleted(eventId);
                result.put("deleteSuccess", deleteSuccess);
            }

            // 4. 测试会议室查询
            List<String> rooms = integrationService.getAvailableMeetingRooms(
                LocalDateTime.now().toString(),
                LocalDateTime.now().plusHours(1).toString()
            );
            result.put("availableRooms", rooms);

            // 5. 测试集成可用性
            boolean available = integrationService.isDingTalkIntegrationAvailable();
            result.put("integrationAvailable", available);

            result.put("success", true);
            result.put("message", "完整集成流程测试完成");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "完整集成流程测试异常: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 创建测试面试对象
     */
    private Interview createTestInterview() {
        Interview interview = new Interview();
        interview.setId(999L);
        interview.setCandidate("测试候选人");
        interview.setCandidateEmail("<EMAIL>");
        interview.setInterviewer("<EMAIL>");
        interview.setInterviewType("TECHNICAL");
        interview.setLocation("ONSITE");
        interview.setScheduledTime(LocalDateTime.now().plusDays(1));
        interview.setRemark("这是一个测试面试");
        return interview;
    }
}
