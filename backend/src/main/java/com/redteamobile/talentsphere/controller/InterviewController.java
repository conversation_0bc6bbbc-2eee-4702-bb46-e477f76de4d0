package com.redteamobile.talentsphere.controller;

import com.redteamobile.talentsphere.dto.InterviewScheduleRequest;
import com.redteamobile.talentsphere.dto.InterviewUpdateRequest;
import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.service.InterviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * Controller for managing interview operations
 */
@RestController
@RequestMapping("/api/interviews")
public class InterviewController {

    @Autowired
    private InterviewService interviewService;

    @GetMapping()
    public ResponseEntity<PageResponse<Interview>> getInterviews(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return ResponseEntity.ok(interviewService.getInterviews(page, pageSize, startDate, endDate));
    }

    /**
     * Get interview by id
     *
     * @param id The interview ID
     * @return The interview with the given ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<Interview> getInterviewById(@PathVariable Long id) {
        Interview interview = interviewService.getInterviewById(id);
        return ResponseEntity.ok(interview);
    }

    /**
     * Schedule a new interview
     * Creates interview record and generates QR code for check-in
     * 
     * @param request Interview details including type, time, and interviewer
     * @return The created Interview object with QR code
     */
    @PostMapping("/schedule")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Interview> scheduleInterview(
            @RequestBody InterviewScheduleRequest request
    ) {
        Interview interview = interviewService.scheduleInterview(
            request.getPositionId(),
            request.getResumeId(),
            request.getCandidate(),
            request.getCandidateEmail(),
            request.getInterviewer(),
            request.getContact(),
            request.getCc(),
            request.getScheduledTime(),
            request.getInterviewType(),
            request.isNotifyCandidate(),
            request.getRemark(),
            request.getLocation(),
            request.isSyncToDingTalk()
        );
        return ResponseEntity.ok(interview);
    }

    /**
     * Get all interviews for a specific resume
     * 
     * @param resumeId The resume ID to get interviews for
     * @return List of all interviews scheduled for the resume
     */
    @GetMapping("/resume/{resumeId}")
    public ResponseEntity<List<Interview>> getInterviewsByResume(@PathVariable Long resumeId) {
        return ResponseEntity.ok(interviewService.getInterviewsByResumeId(resumeId));
    }

    /**
     * Get previous completed interviews for a resume
     *
     * @param resumeId The resume ID to get feedback history for
     * @param currentTime The current interview time (to get only previous interviews)
     * @return List of completed interviews with feedback
     */
    @GetMapping("/resume/{resumeId}/feedback-history")
    public ResponseEntity<List<Interview>> getInterviewFeedbackHistory(
            @PathVariable Long resumeId,
            @RequestParam String currentTime) {
        return ResponseEntity.ok(interviewService.getInterviewFeedbackHistory(resumeId, currentTime));
    }

    /**
     * Submit interview feedback and rating
     * 
     * @param interviewId The interview to update
     * @param rating Numerical rating (1-5)
     * @param feedback Detailed feedback from interviewer
     * @return Empty response with OK status
     */
    @PostMapping("/{interviewId}/feedback")
    public ResponseEntity<Void> provideFeedback(
            @PathVariable Long interviewId,
            @RequestParam Float rating,
            @RequestParam Boolean result,
            @RequestParam String feedback) {
        interviewService.updateInterviewFeedback(interviewId, rating, result, feedback);
        return ResponseEntity.ok().build();
    }

    /**
     * Record candidate check-in for interview
     * Updates interview status when candidate scans QR code
     * 
     * @param interviewId The interview to check in for
     * @return Empty response with OK status
     */
    @PostMapping("/{interviewId}/checkin")
    public ResponseEntity<Void> checkin(@PathVariable Long interviewId) {
        interviewService.recordCheckin(interviewId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/status/{status}")
    public ResponseEntity<PageResponse<Interview>> getInterviewsByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return ResponseEntity.ok(interviewService.getInterviewsByStatus(status, page, pageSize, startDate, endDate));
    }

    /**
     * Cancel an interview
     * Updates interview status to CANCELLED
     * 
     * @param id The interview ID to cancel
     * @return The updated Interview object
     */
    @PatchMapping("/{id}/cancel")
    public ResponseEntity<Interview> cancelInterview(@PathVariable Long id) {
        Interview interview = interviewService.cancelInterview(id);
        return ResponseEntity.ok(interview);
    }

    /**
     * Update an existing interview
     * 
     * @param id The interview ID to update
     * @param request Updated interview details
     * @return The updated Interview object
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ADMIN', 'HR')")
    public ResponseEntity<Interview> updateInterview(
            @PathVariable Long id,
            @RequestBody InterviewUpdateRequest request
    ) {
        Interview interview = interviewService.updateInterview(
            id,
            request.getInterviewer(),
            request.getContact(),
            request.getCc(),
            request.getScheduledTime(),
            request.getInterviewType(),
            request.isNotifyCandidate(),
            request.getRemark(),
            request.getLocation(),
            request.getPositionId(),
            request.isSyncToDingTalk()
        );
        return ResponseEntity.ok(interview);
    }
} 