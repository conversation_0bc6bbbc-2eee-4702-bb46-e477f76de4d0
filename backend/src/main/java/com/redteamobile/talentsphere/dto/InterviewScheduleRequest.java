package com.redteamobile.talentsphere.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class InterviewScheduleRequest {
    private Long resumeId;
    private Long positionId;
    private String candidate;  // name
    private String candidateEmail; // email
    private String interviewer;  // email
    private String contact;  // phone or other contact info
    private List<String> cc;  // email list
    private LocalDateTime scheduledTime;
    private String interviewType;
    private boolean notifyCandidate;
    private String remark;  // Additional notes or remarks
    private String location;
    private boolean syncToDingTalk;  // 是否同步到钉钉日历
} 