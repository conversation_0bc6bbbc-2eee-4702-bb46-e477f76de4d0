package com.redteamobile.talentsphere.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class InterviewUpdateRequest {
    private Long positionId;
    private String interviewer;
    private String contact;
    private List<String> cc;
    private Date scheduledTime;
    private String interviewType;
    private boolean notifyCandidate;
    private String remark;
    private String location;
    private boolean syncToDingTalk;  // 是否同步到钉钉日历
} 