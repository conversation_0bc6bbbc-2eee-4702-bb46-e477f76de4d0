package com.redteamobile.talentsphere.dto.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉访问令牌响应
 */
@Data
public class DingTalkAccessTokenResponse {
    
    @JsonProperty("errcode")
    private Integer errCode;
    
    @JsonProperty("errmsg")
    private String errMsg;
    
    @JsonProperty("access_token")
    private String accessToken;
    
    @JsonProperty("expires_in")
    private Integer expiresIn;
    
    public boolean isSuccess() {
        return errCode != null && errCode == 0;
    }
}
