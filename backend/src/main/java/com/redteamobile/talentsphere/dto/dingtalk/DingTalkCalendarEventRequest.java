package com.redteamobile.talentsphere.dto.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉日历事件请求
 */
@Data
public class DingTalkCalendarEventRequest {
    
    @JsonProperty("summary")
    private String summary; // 事件标题
    
    @JsonProperty("description")
    private String description; // 事件描述
    
    @JsonProperty("start")
    private EventTime start; // 开始时间
    
    @JsonProperty("end")
    private EventTime end; // 结束时间
    
    @JsonProperty("attendees")
    private Attendee[] attendees; // 参会人员
    
    @JsonProperty("organizer")
    private String organizer; // 组织者
    
    @JsonProperty("location")
    private String location; // 地点
    
    @JsonProperty("calendar_id")
    private String calendarId; // 日历ID
    
    @Data
    public static class EventTime {
        @JsonProperty("date_time")
        private String dateTime; // ISO格式时间
        
        @JsonProperty("time_zone")
        private String timeZone; // 时区
    }
    
    @Data
    public static class Attendee {
        @JsonProperty("email")
        private String email; // 参会人邮箱
        
        @JsonProperty("name")
        private String name; // 参会人姓名
        
        @JsonProperty("response_status")
        private String responseStatus; // 响应状态：needsAction, accepted, declined, tentative
    }
}
