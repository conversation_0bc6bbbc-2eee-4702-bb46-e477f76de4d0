package com.redteamobile.talentsphere.dto.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉日历事件响应
 */
@Data
public class DingTalkCalendarEventResponse {
    
    @JsonProperty("errcode")
    private Integer errCode;
    
    @JsonProperty("errmsg")
    private String errMsg;
    
    @JsonProperty("event_id")
    private String eventId; // 事件ID
    
    @JsonProperty("calendar_id")
    private String calendarId; // 日历ID
    
    public boolean isSuccess() {
        return errCode != null && errCode == 0;
    }
}
