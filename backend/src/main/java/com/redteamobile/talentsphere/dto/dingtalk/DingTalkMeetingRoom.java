package com.redteamobile.talentsphere.dto.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉会议室信息
 */
@Data
public class DingTalkMeetingRoom {
    
    @JsonProperty("room_id")
    private String roomId;
    
    @JsonProperty("room_name")
    private String roomName;
    
    @JsonProperty("capacity")
    private Integer capacity;
    
    @JsonProperty("location")
    private String location;
    
    @JsonProperty("equipment")
    private String equipment;
    
    @JsonProperty("is_available")
    private Boolean isAvailable;
}
