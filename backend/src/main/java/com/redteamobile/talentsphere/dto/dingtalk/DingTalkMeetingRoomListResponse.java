package com.redteamobile.talentsphere.dto.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * 钉钉会议室列表响应
 */
@Data
public class DingTalkMeetingRoomListResponse {
    
    @JsonProperty("errcode")
    private Integer errCode;
    
    @JsonProperty("errmsg")
    private String errMsg;
    
    @JsonProperty("result")
    private List<DingTalkMeetingRoom> result;
    
    public boolean isSuccess() {
        return errCode != null && errCode == 0;
    }
}
