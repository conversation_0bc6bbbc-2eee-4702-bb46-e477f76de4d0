package com.redteamobile.talentsphere.dto.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉会议室预订请求
 */
@Data
public class DingTalkMeetingRoomRequest {
    
    @JsonProperty("room_id")
    private String roomId;
    
    @JsonProperty("subject")
    private String subject;
    
    @JsonProperty("start_time")
    private Long startTime;
    
    @JsonProperty("end_time")
    private Long endTime;
    
    @JsonProperty("organizer")
    private String organizer;
    
    @JsonProperty("attendees")
    private String[] attendees;
    
    @JsonProperty("description")
    private String description;
}
