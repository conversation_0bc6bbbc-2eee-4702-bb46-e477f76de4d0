package com.redteamobile.talentsphere.dto.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 钉钉会议室预订响应
 */
@Data
public class DingTalkMeetingRoomResponse {
    
    @JsonProperty("errcode")
    private Integer errCode;
    
    @JsonProperty("errmsg")
    private String errMsg;
    
    @JsonProperty("booking_id")
    private String bookingId;
    
    public boolean isSuccess() {
        return errCode != null && errCode == 0;
    }
}
