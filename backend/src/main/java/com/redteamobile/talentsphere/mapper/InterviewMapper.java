package com.redteamobile.talentsphere.mapper;

import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.dto.DailyCountRecord;
import com.redteamobile.talentsphere.dto.StatusCount;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;

@Mapper
public interface InterviewMapper {

    @Insert("INSERT INTO interviews (resume_id, position_id, candidate, candidate_email, interviewer, contact, cc_list, " +
            "interview_type, scheduled_time, status, rating, feedback, qr_code_url, checkin_time, location, remark) " +
            "VALUES (#{resumeId}, #{positionId}, #{candidate}, #{candidateEmail}, #{interviewer}, #{contact}, #{ccList}, " +
            "#{interviewType}, #{scheduledTime}, #{status}, #{rating}, #{feedback}, #{qrCodeUrl}, " +
            "#{checkinTime}, #{location}, #{remark})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Interview interview);

    @Select("SELECT * FROM interviews WHERE resume_id = #{resumeId}")
    List<Interview> findByResumeId(Long resumeId);

    @Select("SELECT * FROM interviews WHERE id = #{id}")
    Interview findById(Long id);

    @Update("UPDATE interviews SET status = #{status}, rating = #{rating}, feedback = #{feedback}, " +
            "result = #{result}, updated_at = NOW() WHERE id = #{id}")
    void updateInterviewFeedback(Interview interview);

    @Update("UPDATE interviews SET checkin_time = #{checkinTime}, status = 'IN_PROGRESS', updated_at = NOW() WHERE id = #{id}")
    void updateCheckinTime(@Param("id") Long id,
                           @Param("checkinTime") LocalDateTime checkinTime);

    @Update("UPDATE interviews SET " +
            "resume_id = #{resumeId}, " +
            "position_id = #{positionId}, " +
            "candidate = #{candidate}, " +
            "candidate_email = #{candidateEmail}, " +
            "interviewer = #{interviewer}, " +
            "contact = #{contact}, " +
            "cc_list = #{ccList}, " +
            "interview_type = #{interviewType}, " +
            "scheduled_time = #{scheduledTime}, " +
            "status = #{status}, " +
            "rating = #{rating}, " +
            "feedback = #{feedback}, " +
            "qr_code_url = #{qrCodeUrl}, " +
            "checkin_time = #{checkinTime}, " +
            "location = #{location}, " +
            "result = #{result}, " +
            "remark = #{remark}, " +
            "updated_at = #{updatedAt} " +
            "WHERE id = #{id}")
    void update(Interview interview);

    @Select("SELECT * FROM interviews ORDER BY scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    List<Interview> findAllPaginated(@Param("offset") int offset,
                                     @Param("pageSize") int pageSize);

    @Select("SELECT * FROM interviews WHERE status = #{status} ORDER BY scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    List<Interview> findByStatusPaginated(@Param("status") String status,
                                          @Param("offset") int offset,
                                          @Param("pageSize") int pageSize);

    @Select("SELECT * FROM interviews WHERE status = #{status} AND interviewer = #{interviewer} ORDER BY scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    List<Interview> findByStatusAndInterviewerPaginated(@Param("status") String status,
                                                        @Param("interviewer") String interviewer, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("SELECT * FROM interviews WHERE interviewer = #{interviewer} ORDER BY scheduled_time DESC LIMIT #{offset}, #{pageSize}")
    List<Interview> findByInterviewerPaginated(@Param("interviewer") String interviewer,
                                               @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("SELECT * FROM interviews WHERE status = #{status} ORDER BY scheduled_time DESC")
    List<Interview> findByStatus(String status);

    @Select("SELECT * FROM interviews ORDER BY scheduled_time DESC")
    List<Interview> findAll();

    @Select("SELECT * FROM interviews WHERE interviewer = #{interviewer} ORDER BY scheduled_time DESC")
    List<Interview> findByInterviewer(String email);

    @Select("SELECT * FROM interviews WHERE status = #{status} AND interviewer = #{interviewer} ORDER BY scheduled_time DESC")
    List<Interview> findByStatusAndInterviewer(String status, String interviewer);

    @Select("SELECT status, COUNT(*) as count FROM interviews GROUP BY status")
    @Results(id = "statusCountMap", value = {
        @Result(property = "status", column = "status"),
        @Result(property = "count", column = "count")
    })
    List<StatusCount> countByStatusForStatistic();

    @Select("SELECT COUNT(*) FROM interviews WHERE status = #{status}")
    Long countByStatus(@Param("status") String status);

    @Select("SELECT COUNT(*) FROM interviews")
    Long countAll();

    @Select("SELECT COUNT(*) FROM interviews WHERE interviewer = #{interviewer}")
    Long countByInterviewer(@Param("interviewer") String interviewer);

    @Select("SELECT COUNT(*) FROM interviews WHERE status = #{status} AND interviewer = #{interviewer}")
    Long countByStatusAndInterviewer(@Param("status") String status,
                                     @Param("interviewer") String interviewer);

    @Select("SELECT DATE(scheduled_time) as date, COUNT(*) as count " +
            "FROM interviews " +
            "WHERE DATE(scheduled_time) BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY DATE(scheduled_time)")
    List<DailyCountRecord> countDailyInterviews(@Param("startDate") LocalDate startDate,
                                                @Param("endDate") LocalDate endDate);
}