package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.model.Interview;

import java.util.List;

/**
 * 钉钉集成服务接口
 * 提供日历同步、会议室预订等功能
 */
public interface DingTalkService {
    
    /**
     * 创建钉钉日历事件
     * @param interview 面试信息
     * @param organizer 组织者邮箱
     * @return 钉钉事件ID
     */
    String createCalendarEvent(Interview interview, String organizer);
    
    /**
     * 更新钉钉日历事件
     * @param eventId 钉钉事件ID
     * @param interview 更新后的面试信息
     * @param organizer 组织者邮箱
     * @return 是否更新成功
     */
    boolean updateCalendarEvent(String eventId, Interview interview, String organizer);
    
    /**
     * 删除钉钉日历事件
     * @param eventId 钉钉事件ID
     * @return 是否删除成功
     */
    boolean deleteCalendarEvent(String eventId);
    
    /**
     * 获取可用会议室列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 可用会议室列表
     */
    List<String> getAvailableMeetingRooms(String startTime, String endTime);
    
    /**
     * 预订会议室
     * @param roomId 会议室ID
     * @param interview 面试信息
     * @param organizer 组织者邮箱
     * @return 预订是否成功
     */
    boolean bookMeetingRoom(String roomId, Interview interview, String organizer);
    
    /**
     * 验证钉钉配置是否正确
     * @return 配置是否有效
     */
    boolean validateConfiguration();
} 