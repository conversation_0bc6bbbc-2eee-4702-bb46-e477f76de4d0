package com.redteamobile.talentsphere.service;

import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.dto.PageResponse;
import java.util.List;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

public interface InterviewService {
    Interview scheduleInterview(
        Long positionId,
        Long resumeId,
        String candidate,
        String candidateEmail,
        String interviewer,
        String contact,
        List<String> cc,
        LocalDateTime scheduledTime,
        String interviewType,
        boolean notifyCandidate,
        String remark,
        String location,
        boolean syncToDingTalk
    );
    List<Interview> getInterviewsByResumeId(Long resumeId);
    void updateInterviewFeedback(Long interviewId, Float rating, Boolean result, String feedback);
    void recordCheckin(Long interviewId);
    String generateInterviewQRCode(Long interviewId);
    PageResponse<Interview> getInterviews(int page, int pageSize);
    PageResponse<Interview> getInterviews(int page, int pageSize, LocalDate startDate, LocalDate endDate);
    PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize);
    PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize, LocalDate startDate, LocalDate endDate);
    Interview cancelInterview(Long id);
    List<Interview> getInterviewFeedbackHistory(Long resumeId, String currentTime);
    Interview updateInterview(Long id, String interviewer, String contact, List<String> cc, 
                            Date scheduledTime, String interviewType, boolean notifyCandidate, 
                            String remark, String location, Long positionId, boolean syncToDingTalk);
    Interview getInterviewById(Long id);
} 