package com.redteamobile.talentsphere.service.impl;

import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.service.DingTalkService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 面试钉钉集成服务
 * 提供面试与钉钉平台的集成功能
 */
@Service
public class InterviewDingTalkIntegrationService {
    
    private static final Logger log = LoggerFactory.getLogger(InterviewDingTalkIntegrationService.class);
    
    @Autowired
    private DingTalkService dingTalkService;
    
    /**
     * 创建面试时的钉钉集成处理
     * @param interview 面试对象
     * @param organizer 组织者邮箱
     * @return 钉钉事件ID，失败时返回null
     */
    public String handleInterviewCreated(Interview interview, String organizer) {
        try {
            log.info("处理面试创建的钉钉集成: 面试ID={}", interview.getId());
            
            // 1. 创建日历事件
            String eventId = dingTalkService.createCalendarEvent(interview, organizer);
            if (eventId != null) {
                log.info("面试日历事件创建成功: 面试ID={}, 事件ID={}", interview.getId(), eventId);
            } else {
                log.warn("面试日历事件创建失败: 面试ID={}", interview.getId());
            }
            
            // 2. 如果是现场面试，尝试预订会议室
            if ("ONSITE".equals(interview.getLocation())) {
                boolean roomBooked = handleMeetingRoomBooking(interview, organizer);
                if (roomBooked) {
                    log.info("会议室预订成功: 面试ID={}", interview.getId());
                } else {
                    log.warn("会议室预订失败: 面试ID={}", interview.getId());
                }
            }
            
            return eventId;
            
        } catch (Exception e) {
            log.error("处理面试创建的钉钉集成失败: 面试ID=" + interview.getId(), e);
            return null;
        }
    }
    
    /**
     * 更新面试时的钉钉集成处理
     * @param interview 更新后的面试对象
     * @param eventId 原有的钉钉事件ID
     * @param organizer 组织者邮箱
     * @return 是否成功
     */
    public boolean handleInterviewUpdated(Interview interview, String eventId, String organizer) {
        try {
            log.info("处理面试更新的钉钉集成: 面试ID={}, 事件ID={}", interview.getId(), eventId);
            
            if (eventId == null || eventId.trim().isEmpty()) {
                log.warn("事件ID为空，无法更新钉钉日历事件: 面试ID={}", interview.getId());
                return false;
            }
            
            // 更新日历事件
            boolean success = dingTalkService.updateCalendarEvent(eventId, interview, organizer);
            if (success) {
                log.info("面试日历事件更新成功: 面试ID={}, 事件ID={}", interview.getId(), eventId);
            } else {
                log.warn("面试日历事件更新失败: 面试ID={}, 事件ID={}", interview.getId(), eventId);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("处理面试更新的钉钉集成失败: 面试ID=" + interview.getId() + ", 事件ID=" + eventId, e);
            return false;
        }
    }
    
    /**
     * 删除面试时的钉钉集成处理
     * @param eventId 钉钉事件ID
     * @return 是否成功
     */
    public boolean handleInterviewDeleted(String eventId) {
        try {
            log.info("处理面试删除的钉钉集成: 事件ID={}", eventId);
            
            if (eventId == null || eventId.trim().isEmpty()) {
                log.warn("事件ID为空，无法删除钉钉日历事件");
                return false;
            }
            
            // 删除日历事件
            boolean success = dingTalkService.deleteCalendarEvent(eventId);
            if (success) {
                log.info("面试日历事件删除成功: 事件ID={}", eventId);
            } else {
                log.warn("面试日历事件删除失败: 事件ID={}", eventId);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("处理面试删除的钉钉集成失败: 事件ID=" + eventId, e);
            return false;
        }
    }
    
    /**
     * 获取可用会议室
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 可用会议室列表
     */
    public List<String> getAvailableMeetingRooms(String startTime, String endTime) {
        try {
            log.info("获取可用会议室: 开始时间={}, 结束时间={}", startTime, endTime);
            
            List<String> rooms = dingTalkService.getAvailableMeetingRooms(startTime, endTime);
            log.info("获取到可用会议室数量: {}", rooms.size());
            
            return rooms;
            
        } catch (Exception e) {
            log.error("获取可用会议室失败", e);
            return dingTalkService.getAvailableMeetingRooms(startTime, endTime); // 返回默认列表
        }
    }
    
    /**
     * 处理会议室预订
     * @param interview 面试对象
     * @param organizer 组织者
     * @return 是否成功
     */
    private boolean handleMeetingRoomBooking(Interview interview, String organizer) {
        try {
            // 获取可用会议室
            String startTime = interview.getScheduledTime().toString();
            String endTime = interview.getScheduledTime().plusHours(1).toString();
            
            List<String> availableRooms = getAvailableMeetingRooms(startTime, endTime);
            
            if (availableRooms.isEmpty()) {
                log.warn("没有可用的会议室: 面试ID={}", interview.getId());
                return false;
            }
            
            // 选择第一个可用的会议室
            String selectedRoom = availableRooms.get(0);
            
            // 预订会议室
            boolean success = dingTalkService.bookMeetingRoom(selectedRoom, interview, organizer);
            
            if (success) {
                log.info("会议室预订成功: 面试ID={}, 会议室={}", interview.getId(), selectedRoom);
            } else {
                log.warn("会议室预订失败: 面试ID={}, 会议室={}", interview.getId(), selectedRoom);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("处理会议室预订失败: 面试ID=" + interview.getId(), e);
            return false;
        }
    }
    
    /**
     * 检查钉钉集成是否可用
     * @return 是否可用
     */
    public boolean isDingTalkIntegrationAvailable() {
        return dingTalkService.validateConfiguration();
    }
}
