package com.redteamobile.talentsphere.service.impl;

import com.google.common.collect.Lists;
import com.redteamobile.talentsphere.dto.PageResponse;
import com.redteamobile.talentsphere.mapper.InterviewMapper;
import com.redteamobile.talentsphere.mapper.UserMapper;
import com.redteamobile.talentsphere.model.Interview;
import com.redteamobile.talentsphere.model.User;
import com.redteamobile.talentsphere.service.InterviewService;
import com.redteamobile.talentsphere.service.DingTalkService;
import com.redteamobile.talentsphere.service.EmailService;
import com.redteamobile.talentsphere.service.ResumeOperationLogService;
import com.redteamobile.talentsphere.service.NotificationService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Collections;

@Service
public class InterviewServiceImpl implements InterviewService {

    public static final List<String> FULL_ACCESS_ROLES = Lists.newArrayList("ADMIN", "HR");

    @Autowired
    private InterviewMapper interviewMapper;
    @Autowired
    private EmailService emailService;
    @Autowired
    private ResumeOperationLogService resumeOperationLogService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private DingTalkService dingTalkService;

    @Override
    @Transactional
    public Interview scheduleInterview(
        Long positionId,
        Long resumeId,
        String candidate,
        String candidateEmail,
        String interviewer,
        String contact,
        List<String> cc,
        LocalDateTime scheduledTime,
        String interviewType,
        boolean notifyCandidate,
        String remark,
        String location,
        boolean syncToDingTalk
    ) {
        Interview interview = new Interview();
        interview.setResumeId(resumeId);
        interview.setPositionId(positionId);
        interview.setCandidate(candidate);
        interview.setCandidateEmail(candidateEmail);
        interview.setInterviewer(interviewer);
        interview.setContact(contact);
        interview.setCcList(String.join(",", cc));
        interview.setScheduledTime(scheduledTime);
        interview.setInterviewType(interviewType);
        interview.setStatus("SCHEDULED");
        interview.setRemark(remark);
        interview.setLocation(location);
        interview.setSyncToDingTalk(syncToDingTalk);

        interviewMapper.insert(interview);

        if (syncToDingTalk) {
            try {
                String organizer = getCurrentUser().getEmail();
                String eventId = dingTalkService.createCalendarEvent(interview, organizer);
                if (eventId != null) {
                    interview.setDingTalkEventId(eventId);
                    interviewMapper.update(interview);
                }
            } catch (Exception e) {
                System.err.println("钉钉日历同步失败: " + e.getMessage());
            }
        }

        String operatedBy = getCurrentUser().getEmail();
        resumeOperationLogService.recordOperation(
            resumeId,
            "SCHEDULE",
            operatedBy,
            String.format("安排了%s面试，面试官为%s，面试时间为%s%s", 
                interviewType.equals("TECHNICAL") ? "技术" : 
                interviewType.equals("HR") ? "HR" : "其他",
                interviewer, 
                scheduledTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")),
                syncToDingTalk ? "，已同步到钉钉日历" : "")
        );
        
        // Send notification to interviewer
        notificationService.createInterviewScheduleNotification(
            operatedBy, 
            interviewer, 
            interview.getId(), 
            candidate, 
            scheduledTime.toString()
        );

        if (notifyCandidate) {
            emailService.sendInterviewInvitation(candidateEmail, interview);
        }

        return interview;
    }

    @Override
    public List<Interview> getInterviewsByResumeId(Long resumeId) {
        return interviewMapper.findByResumeId(resumeId);
    }

    @Override
    public void updateInterviewFeedback(Long interviewId, Float rating, Boolean result, String feedback) {
        Interview interview = interviewMapper.findById(interviewId);
        if (interview == null) {
            throw new RuntimeException("Interview not found");
        }
        
        interview.setRating(rating);
        interview.setFeedback(feedback);
        interview.setResult(result);
        interview.setStatus("COMPLETED");
        interviewMapper.updateInterviewFeedback(interview);
        
        User user = getCurrentUser();
        List<User> hrUsers = getHrUsers();
        
        for (User hrUser : hrUsers) {
            if (!hrUser.getEmail().equals(user.getEmail())) {
                notificationService.createInterviewFeedbackNotification(
                    user.getEmail(),
                    hrUser.getEmail(),
                    interviewId,
                    user.getEmail(),
                    interview.getCandidate()
                );
            }
        }
    }

    @Override
    public void recordCheckin(Long interviewId) {
        interviewMapper.updateCheckinTime(interviewId, LocalDateTime.now());
    }

    @Override
    public String generateInterviewQRCode(Long interviewId) {
        return "qr-code-url-" + interviewId;
    }

    @Override
    public PageResponse<Interview> getInterviews(int page, int pageSize) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;
        
        if (canFullAccess(user)) {
            interviews = interviewMapper.findAllPaginated(offset, pageSize);
            total = interviewMapper.countAll();
        } else {
            interviews = interviewMapper.findByInterviewerPaginated(user.getEmail(), offset, pageSize);
            total = interviewMapper.countByInterviewer(user.getEmail());
        }
        
        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public PageResponse<Interview> getInterviews(int page, int pageSize, LocalDate startDate, LocalDate endDate) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;
        
        if (startDate == null && endDate == null) {
            return getInterviews(page, pageSize);
        }
        
        LocalDateTime startDateTime = startDate != null 
            ? startDate.atStartOfDay() 
            : LocalDateTime.now().minusYears(10);
        
        LocalDateTime endDateTime = endDate != null 
            ? endDate.plusDays(1).atStartOfDay() 
            : LocalDateTime.now().plusYears(10);
        
        if (canFullAccess(user)) {
            interviews = interviewMapper.findByDateRangePaginated(startDateTime, endDateTime, offset, pageSize);
            total = interviewMapper.countByDateRange(startDateTime, endDateTime);
        } else {
            interviews = interviewMapper.findByDateRangeAndInterviewerPaginated(
                startDateTime, endDateTime, user.getEmail(), offset, pageSize);
            total = interviewMapper.countByDateRangeAndInterviewer(startDateTime, endDateTime, user.getEmail());
        }
        
        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;
        
        if (canFullAccess(user)) {
            interviews = interviewMapper.findByStatusPaginated(status, offset, pageSize);
            total = interviewMapper.countByStatus(status);
        } else {
            interviews = interviewMapper.findByStatusAndInterviewerPaginated(status, user.getEmail(), offset, pageSize);
            total = interviewMapper.countByStatusAndInterviewer(status, user.getEmail());
        }
        
        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }
    
    @Override
    public PageResponse<Interview> getInterviewsByStatus(String status, int page, int pageSize, LocalDate startDate, LocalDate endDate) {
        User user = getCurrentUser();
        List<Interview> interviews;
        long total;
        int offset = (page - 1) * pageSize;
        
        if (startDate == null && endDate == null) {
            return getInterviewsByStatus(status, page, pageSize);
        }
        
        LocalDateTime startDateTime = startDate != null 
            ? startDate.atStartOfDay() 
            : LocalDateTime.now().minusYears(10);
        
        LocalDateTime endDateTime = endDate != null 
            ? endDate.plusDays(1).atStartOfDay() 
            : LocalDateTime.now().plusYears(10);
        
        if (canFullAccess(user)) {
            interviews = interviewMapper.findByStatusAndDateRangePaginated(
                status, startDateTime, endDateTime, offset, pageSize);
            total = interviewMapper.countByStatusAndDateRange(status, startDateTime, endDateTime);
        } else {
            interviews = interviewMapper.findByStatusAndDateRangeAndInterviewerPaginated(
                status, startDateTime, endDateTime, user.getEmail(), offset, pageSize);
            total = interviewMapper.countByStatusAndDateRangeAndInterviewer(
                status, startDateTime, endDateTime, user.getEmail());
        }
        
        return PageResponse.<Interview>builder()
            .data(interviews)
            .total(total)
            .page(page)
            .pageSize(pageSize)
            .build();
    }

    @Override
    public Interview cancelInterview(Long id) {
        Interview interview = interviewMapper.findById(id);
        if (interview == null) {
            throw new RuntimeException("Interview not found");
        }
        
        interview.setStatus("CANCELLED");
        interview.setUpdatedAt(LocalDateTime.now());
        
        interviewMapper.update(interview);
        return interview;
    }

    @Override
    @Transactional
    public Interview updateInterview(
        Long id,
        String interviewer,
        String contact,
        List<String> cc,
        Date scheduledTime,
        String interviewType,
        boolean notifyCandidate,
        String remark,
        String location,
        Long positionId,
        boolean syncToDingTalk  // 新增钉钉同步参数
    ) {
        Interview interview = interviewMapper.findById(id);
        if (interview == null) {
            throw new RuntimeException("Interview not found");
        }
        
        interview.setInterviewer(interviewer);
        interview.setContact(contact);
        interview.setCcList(String.join(",", cc));
        interview.setScheduledTime(scheduledTime.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
        interview.setInterviewType(interviewType);
        interview.setRemark(remark);
        interview.setLocation(location);
        interview.setPositionId(positionId);
        interview.setSyncToDingTalk(syncToDingTalk);
        interview.setUpdatedAt(LocalDateTime.now());
        
        interviewMapper.update(interview);
        
        // 钉钉日历同步处理
        if (syncToDingTalk) {
            try {
                String organizer = getCurrentUser().getEmail();
                if (interview.getDingTalkEventId() != null) {
                    // 更新现有事件
                    dingTalkService.updateCalendarEvent(interview.getDingTalkEventId(), interview, organizer);
                } else {
                    // 创建新事件
                    String eventId = dingTalkService.createCalendarEvent(interview, organizer);
                    if (eventId != null) {
                        interview.setDingTalkEventId(eventId);
                        interviewMapper.update(interview);
                    }
                }
            } catch (Exception e) {
                System.err.println("钉钉日历同步失败: " + e.getMessage());
            }
        } else if (interview.getDingTalkEventId() != null) {
            // 如果取消同步且之前有事件，则删除钉钉事件
            try {
                dingTalkService.deleteCalendarEvent(interview.getDingTalkEventId());
                interview.setDingTalkEventId(null);
                interviewMapper.update(interview);
            } catch (Exception e) {
                System.err.println("删除钉钉日历事件失败: " + e.getMessage());
            }
        }
        
        String operatedBy = getCurrentUser().getEmail();
        resumeOperationLogService.recordOperation(
            interview.getResumeId(),
            "更新面试",
            operatedBy,
            String.format("更新了%s面试，面试官为%s，面试时间为%s", 
                interviewType.equals("TECHNICAL") ? "技术" : 
                interviewType.equals("HR") ? "HR" : "其他",
                interviewer, 
                interview.getScheduledTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm")))
        );
        
        User user = getCurrentUser();
        if (!user.getEmail().equals(interviewer)) {
            notificationService.createInterviewUpdateNotification(
                operatedBy,
                interviewer,
                id,
                interview.getCandidate(),
                interview.getScheduledTime().toString()
            );
        }
        
        if (notifyCandidate && interview.getCandidateEmail() != null) {
            emailService.sendInterviewInvitation(interview.getCandidateEmail(), interview);
        }
        
        return interview;
    }

    @Override
    public Interview getInterviewById(Long id) {
        Interview interview = interviewMapper.findById(id);
        if (interview == null) {
            throw new RuntimeException("Interview not found with id: " + id);
        }
        
        User user = getCurrentUser();
        if (!canFullAccess(user) && !user.getEmail().equals(interview.getInterviewer())) {
            throw new RuntimeException("You don't have permission to view this interview");
        }
        
        return interview;
    }

    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return (User) authentication.getPrincipal();
    }

    private boolean canFullAccess(User user) {
        return FULL_ACCESS_ROLES.contains(user.getRole());
    }

    private List<User> getHrUsers() {
        return userMapper.findByRole("HR");
    }
    
    @Override
    public List<Interview> getInterviewFeedbackHistory(Long resumeId, String currentTime) {
        LocalDateTime currentDateTime = LocalDateTime.parse(currentTime);
        return interviewMapper.findCompletedInterviewsBeforeTime(resumeId, "COMPLETED", currentDateTime);
    }
} 