package com.redteamobile.talentsphere.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.redteamobile.talentsphere.config.DingTalkConfig;
import com.redteamobile.talentsphere.dto.dingtalk.*;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉API客户端工具类
 * 负责与钉钉API进行交互
 */
@Component
public class DingTalkApiClient {

    private static final Logger log = LoggerFactory.getLogger(DingTalkApiClient.class);

    private static final String GET_TOKEN_URL = "/gettoken";
    private static final String MEETING_ROOM_LIST_URL = "/topapi/meeting/room/list";
    private static final String MEETING_ROOM_BOOK_URL = "/topapi/meeting/room/book";
    private static final String CALENDAR_EVENT_CREATE_URL = "/topapi/calendar/event/create";
    private static final String CALENDAR_EVENT_UPDATE_URL = "/topapi/calendar/event/update";
    private static final String CALENDAR_EVENT_DELETE_URL = "/topapi/calendar/event/delete";

    @Autowired
    private DingTalkConfig dingTalkConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    private String accessToken;
    private long tokenExpireTime;
    
    /**
     * 获取访问令牌
     * @return 访问令牌
     */
    public String getAccessToken() {
        if (!dingTalkConfig.isEnabled()) {
            log.warn("钉钉集成未启用");
            return null;
        }

        // 检查token是否过期
        if (accessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            return accessToken;
        }

        try {
            String url = dingTalkConfig.getApi().getBaseUrl() + GET_TOKEN_URL +
                        "?appkey=" + dingTalkConfig.getApp().getKey() +
                        "&appsecret=" + dingTalkConfig.getApp().getSecret();

            HttpGet httpGet = new HttpGet(url);

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                DingTalkAccessTokenResponse tokenResponse = objectMapper.readValue(responseBody, DingTalkAccessTokenResponse.class);

                if (tokenResponse.isSuccess()) {
                    this.accessToken = tokenResponse.getAccessToken();
                    // 提前5分钟过期
                    this.tokenExpireTime = System.currentTimeMillis() + (tokenResponse.getExpiresIn() - 300) * 1000L;
                    log.info("钉钉访问令牌获取成功");
                    return accessToken;
                } else {
                    log.error("获取钉钉访问令牌失败: {} - {}", tokenResponse.getErrCode(), tokenResponse.getErrMsg());
                    return null;
                }
            }

        } catch (Exception e) {
            log.error("获取钉钉访问令牌失败", e);
            return null;
        }
    }
    
    /**
     * 创建日历事件
     * @param accessToken 访问令牌
     * @param eventData 事件数据
     * @return 事件ID
     */
    public String createCalendarEvent(String accessToken, Map<String, Object> eventData) {
        try {
            log.info("正在创建钉钉日历事件...");

            if (accessToken == null) {
                accessToken = getAccessToken();
                if (accessToken == null) {
                    log.error("无法获取访问令牌，创建日历事件失败");
                    return null;
                }
            }

            String url = dingTalkConfig.getApi().getBaseUrl() + CALENDAR_EVENT_CREATE_URL + "?access_token=" + accessToken;

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            // 构建日历事件请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("summary", eventData.get("summary"));
            requestBody.put("description", eventData.get("description"));
            requestBody.put("organizer", eventData.get("organizer"));
            requestBody.put("location", eventData.get("location"));

            // 处理时间
            Map<String, Object> startTime = new HashMap<>();
            startTime.put("date_time", eventData.get("startTime"));
            startTime.put("time_zone", "Asia/Shanghai");
            requestBody.put("start", startTime);

            Map<String, Object> endTime = new HashMap<>();
            endTime.put("date_time", eventData.get("endTime"));
            endTime.put("time_zone", "Asia/Shanghai");
            requestBody.put("end", endTime);

            // 处理参会人员
            if (eventData.containsKey("attendees")) {
                requestBody.put("attendees", eventData.get("attendees"));
            }

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                DingTalkCalendarEventResponse eventResponse = objectMapper.readValue(responseBody, DingTalkCalendarEventResponse.class);

                if (eventResponse.isSuccess()) {
                    String eventId = eventResponse.getEventId();
                    log.info("成功创建钉钉日历事件，事件ID: {}", eventId);
                    return eventId;
                } else {
                    log.error("创建钉钉日历事件失败: {} - {}", eventResponse.getErrCode(), eventResponse.getErrMsg());
                    return null;
                }
            }

        } catch (Exception e) {
            log.error("创建钉钉日历事件失败", e);
            return null;
        }
    }
    
    /**
     * 更新日历事件
     * @param accessToken 访问令牌
     * @param eventId 事件ID
     * @param eventData 事件数据
     * @return 是否成功
     */
    public boolean updateCalendarEvent(String accessToken, String eventId, Map<String, Object> eventData) {
        try {
            log.info("正在更新钉钉日历事件: {}", eventId);

            if (accessToken == null) {
                accessToken = getAccessToken();
                if (accessToken == null) {
                    log.error("无法获取访问令牌，更新日历事件失败");
                    return false;
                }
            }

            String url = dingTalkConfig.getApi().getBaseUrl() + CALENDAR_EVENT_UPDATE_URL + "?access_token=" + accessToken;

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            // 构建更新请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("event_id", eventId);
            requestBody.put("summary", eventData.get("summary"));
            requestBody.put("description", eventData.get("description"));
            requestBody.put("organizer", eventData.get("organizer"));
            requestBody.put("location", eventData.get("location"));

            // 处理时间
            Map<String, Object> startTime = new HashMap<>();
            startTime.put("date_time", eventData.get("startTime"));
            startTime.put("time_zone", "Asia/Shanghai");
            requestBody.put("start", startTime);

            Map<String, Object> endTime = new HashMap<>();
            endTime.put("date_time", eventData.get("endTime"));
            endTime.put("time_zone", "Asia/Shanghai");
            requestBody.put("end", endTime);

            // 处理参会人员
            if (eventData.containsKey("attendees")) {
                requestBody.put("attendees", eventData.get("attendees"));
            }

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                DingTalkCalendarEventResponse eventResponse = objectMapper.readValue(responseBody, DingTalkCalendarEventResponse.class);

                if (eventResponse.isSuccess()) {
                    log.info("成功更新钉钉日历事件，事件ID: {}", eventId);
                    return true;
                } else {
                    log.error("更新钉钉日历事件失败: {} - {}", eventResponse.getErrCode(), eventResponse.getErrMsg());
                    return false;
                }
            }

        } catch (Exception e) {
            log.error("更新钉钉日历事件失败", e);
            return false;
        }
    }
    
    /**
     * 删除日历事件
     * @param accessToken 访问令牌
     * @param eventId 事件ID
     * @return 是否成功
     */
    public boolean deleteCalendarEvent(String accessToken, String eventId) {
        try {
            log.info("正在删除钉钉日历事件: {}", eventId);

            if (accessToken == null) {
                accessToken = getAccessToken();
                if (accessToken == null) {
                    log.error("无法获取访问令牌，删除日历事件失败");
                    return false;
                }
            }

            String url = dingTalkConfig.getApi().getBaseUrl() + CALENDAR_EVENT_DELETE_URL + "?access_token=" + accessToken;

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            // 构建删除请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("event_id", eventId);
            // 注意：删除事件通常需要日历ID，这里使用默认值或从配置获取
            requestBody.put("calendar_id", "primary"); // 使用主日历

            String jsonBody = objectMapper.writeValueAsString(requestBody);
            httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                DingTalkCalendarEventResponse eventResponse = objectMapper.readValue(responseBody, DingTalkCalendarEventResponse.class);

                if (eventResponse.isSuccess()) {
                    log.info("成功删除钉钉日历事件，事件ID: {}", eventId);
                    return true;
                } else {
                    log.error("删除钉钉日历事件失败: {} - {}", eventResponse.getErrCode(), eventResponse.getErrMsg());
                    return false;
                }
            }

        } catch (Exception e) {
            log.error("删除钉钉日历事件失败", e);
            return false;
        }
    }
    
    /**
     * 获取可用会议室
     * @param accessToken 访问令牌
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会议室列表
     */
    public Map<String, Object> getAvailableMeetingRooms(String accessToken, String startTime, String endTime) {
        try {
            log.info("正在获取可用会议室...");

            if (accessToken == null) {
                accessToken = getAccessToken();
                if (accessToken == null) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", false);
                    result.put("error", "无法获取访问令牌");
                    return result;
                }
            }

            String url = dingTalkConfig.getApi().getBaseUrl() + MEETING_ROOM_LIST_URL + "?access_token=" + accessToken;

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("start_time", startTime);
            requestBody.put("end_time", endTime);
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                DingTalkMeetingRoomListResponse roomResponse = objectMapper.readValue(responseBody, DingTalkMeetingRoomListResponse.class);

                Map<String, Object> result = new HashMap<>();
                if (roomResponse.isSuccess()) {
                    List<String> roomNames = roomResponse.getResult().stream()
                            .filter(DingTalkMeetingRoom::getIsAvailable)
                            .map(DingTalkMeetingRoom::getRoomName)
                            .collect(Collectors.toList());
                    result.put("success", true);
                    result.put("rooms", roomNames);
                    log.info("成功获取可用会议室: {}", roomNames);
                } else {
                    result.put("success", false);
                    result.put("error", roomResponse.getErrMsg());
                    log.error("获取会议室列表失败: {} - {}", roomResponse.getErrCode(), roomResponse.getErrMsg());
                }

                return result;
            }

        } catch (Exception e) {
            log.error("获取可用会议室失败", e);
            // 返回默认会议室列表作为后备方案
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("rooms", dingTalkConfig.getMeetingRoom().getDefaultRooms());
            result.put("fallback", true);
            return result;
        }
    }
    
    /**
     * 预订会议室
     * @param accessToken 访问令牌
     * @param roomId 会议室ID
     * @param bookingData 预订数据
     * @return 是否成功
     */
    public boolean bookMeetingRoom(String accessToken, String roomId, Map<String, Object> bookingData) {
        try {
            log.info("正在预订会议室: {}", roomId);

            if (accessToken == null) {
                accessToken = getAccessToken();
                if (accessToken == null) {
                    log.error("无法获取访问令牌，预订会议室失败");
                    return false;
                }
            }

            String url = dingTalkConfig.getApi().getBaseUrl() + MEETING_ROOM_BOOK_URL + "?access_token=" + accessToken;

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");

            // 构建预订请求
            DingTalkMeetingRoomRequest request = new DingTalkMeetingRoomRequest();
            request.setRoomId(roomId);
            request.setSubject((String) bookingData.get("subject"));
            request.setStartTime((Long) bookingData.get("startTime"));
            request.setEndTime((Long) bookingData.get("endTime"));
            request.setOrganizer((String) bookingData.get("organizer"));
            request.setDescription((String) bookingData.get("description"));

            if (bookingData.containsKey("attendees")) {
                request.setAttendees((String[]) bookingData.get("attendees"));
            }

            String jsonBody = objectMapper.writeValueAsString(request);
            httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

                DingTalkMeetingRoomResponse bookingResponse = objectMapper.readValue(responseBody, DingTalkMeetingRoomResponse.class);

                if (bookingResponse.isSuccess()) {
                    log.info("成功预订会议室，预订ID: {}", bookingResponse.getBookingId());
                    return true;
                } else {
                    log.error("预订会议室失败: {} - {}", bookingResponse.getErrCode(), bookingResponse.getErrMsg());
                    return false;
                }
            }

        } catch (Exception e) {
            log.error("预订会议室失败", e);
            return false;
        }
    }
    
    /**
     * 创建日历事件
     */
    public DingTalkCalendarEventResponse createCalendarEvent(DingTalkCalendarEventRequest request) throws IOException {
        String token = getAccessToken();
        String url = dingTalkConfig.getApi().getBaseUrl() + CALENDAR_EVENT_CREATE_URL + "?access_token=" + token;

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");

        String jsonBody = objectMapper.writeValueAsString(request);
        httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

            return objectMapper.readValue(responseBody, DingTalkCalendarEventResponse.class);
        }
    }

    /**
     * 更新日历事件
     */
    public DingTalkCalendarEventResponse updateCalendarEvent(String eventId, DingTalkCalendarEventRequest request) throws IOException {
        String token = getAccessToken();
        String url = dingTalkConfig.getApi().getBaseUrl() + CALENDAR_EVENT_UPDATE_URL + "?access_token=" + token;

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");

        // 添加事件ID到请求中
        Map<String, Object> requestMap = objectMapper.convertValue(request, Map.class);
        requestMap.put("event_id", eventId);

        String jsonBody = objectMapper.writeValueAsString(requestMap);
        httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

            return objectMapper.readValue(responseBody, DingTalkCalendarEventResponse.class);
        }
    }

    /**
     * 删除日历事件
     */
    public DingTalkCalendarEventResponse deleteCalendarEvent(String eventId, String calendarId) throws IOException {
        String token = getAccessToken();
        String url = dingTalkConfig.getApi().getBaseUrl() + CALENDAR_EVENT_DELETE_URL + "?access_token=" + token;

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("event_id", eventId);
        requestBody.put("calendar_id", calendarId);

        String jsonBody = objectMapper.writeValueAsString(requestBody);
        httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);

            return objectMapper.readValue(responseBody, DingTalkCalendarEventResponse.class);
        }
    }

    /**
     * 验证API配置
     * @return 是否配置正确
     */
    public boolean validateConfiguration() {
        return dingTalkConfig.isEnabled() &&
               dingTalkConfig.getApp().getKey() != null &&
               !dingTalkConfig.getApp().getKey().trim().isEmpty() &&
               dingTalkConfig.getApp().getSecret() != null &&
               !dingTalkConfig.getApp().getSecret().trim().isEmpty();
    }

    /**
     * 关闭HTTP客户端资源
     */
    @PreDestroy
    public void destroy() {
        try {
            if (httpClient != null) {
                httpClient.close();
                log.info("钉钉API客户端资源已释放");
            }
        } catch (IOException e) {
            log.error("关闭钉钉API客户端失败", e);
        }
    }
}