# JWT Configuration
jwt.signing.key=hddnHq7B28Jd501uHYgo0GzEDLm13Q7DXfKiZCnJ8STD7a2ETyDBm/erYmvSfwwi7+PujmKahRx6xgSbd9FLEFY5XD98dkVoeIuKdeBl8aNQcrKk0Xct9QAtCFAiy/heyB1rCtRy1zQfuMb8EW2KUoWwoTL7Ie6nzcJPurePOTOEir83WXio1BHDvBQiJfNzORrP/GTmk2iS15KGIIg7EPb/4tUJXk1jDH+HZ0wc90jQaigu2TyjIQdx0/X5ThnnFqpKOYjjrhUvqdz480GS0os6fl0CYTizEM93xN0Uvp1Hobz24C/yocqKaxh//fFfeUStxAcN3Jfm2680gfKICA==
jwt.expiration=86400000

# Server Configuration
server.port=8095

# CORS Configuration for Production
spring.mvc.cors.allowed-origins=https://talentsphere.redteatest.com
spring.mvc.cors.allowed-methods=GET,POST,PUT,DELETE,PATCH,OPTIONS
spring.mvc.cors.allowed-headers=*
spring.mvc.cors.allow-credentials=true
spring.mvc.cors.max-age=3600 